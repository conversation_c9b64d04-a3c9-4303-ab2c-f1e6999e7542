# Human-in-the-Loop (HITL) Implementation

## Overview

This document describes the implementation of Human-in-the-Loop (HITL) functionality in the LangGraph Travel Agent project. The HITL system replaces the previous prompt-based confirmation system with proper interrupt-based user confirmations for booking, payment, and cancellation operations.

## Architecture

### Core Components

1. **HITL Nodes** (`backend/agents/hitl_nodes.py`)
   - `booking_confirmation_node`: Handles booking confirmations
   - `payment_confirmation_node`: Handles payment confirmations  
   - `cancellation_confirmation_node`: Handles cancellation confirmations
   - `execute_booking_node`: Executes confirmed bookings
   - `execute_payment_node`: Executes confirmed payments
   - `execute_cancellation_node`: Executes confirmed cancellations

2. **HITL Tools** (`backend/tools/hitl_tools.py`)
   - `book_hotel_room_hitl`: Prepares hotel booking for HITL confirmation
   - `book_flight_hitl`: Prepares flight booking for HITL confirmation
   - `book_tour_hitl`: Prepares tour booking for HITL confirmation
   - `process_payment_hitl`: Prepares payment for HITL confirmation
   - `cancel_booking_hitl`: Prepares cancellation for HITL confirmation
   - `execute_confirmed_*`: Execution tools for confirmed actions

3. **State Management** (`backend/agents/state.py`)
   - Added HITL-specific fields to State class:
     - `pending_booking`: Stores booking data awaiting confirmation
     - `pending_payment`: Stores payment data awaiting confirmation
     - `pending_cancellation`: Stores cancellation data awaiting confirmation
     - `hitl_action`: Current HITL action type

4. **Graph Integration** (`backend/agents/graph.py`)
   - Updated graph structure to include HITL nodes
   - Modified routing logic to handle HITL flow
   - Replaced original booking/payment tools with HITL versions

5. **API Updates** (`backend/api/v1/response.py`)
   - Added support for interrupt detection
   - Added Command resume handling for HITL responses
   - Updated response models to include interrupt data

6. **Telegram Bot Updates** (`frontend/telegram/bot.py`)
   - Added HITL interrupt handling
   - Created inline keyboard confirmations
   - Added callback handlers for user decisions
   - Formatted confirmation messages for different action types

## HITL Flow

### 1. Booking Flow
```
User Request → HITL Tool → Interrupt → UI Confirmation → Execute/Reject
```

1. User requests booking (hotel/flight/tour)
2. Agent calls appropriate HITL booking tool
3. Tool prepares booking data and triggers interrupt
4. Telegram bot displays confirmation with inline buttons
5. User approves/rejects via button click
6. Graph resumes with user decision
7. If approved, execute booking; if rejected, cancel

### 2. Payment Flow
```
Payment Request → HITL Tool → Interrupt → UI Confirmation → Execute/Reject
```

1. User requests payment for existing booking
2. Agent calls `process_payment_hitl` tool
3. Tool prepares payment data and triggers interrupt
4. Telegram bot displays payment confirmation
5. User approves/rejects payment
6. Graph resumes and processes payment or cancels

### 3. Cancellation Flow
```
Cancel Request → HITL Tool → Interrupt → UI Confirmation → Execute/Reject
```

1. User requests booking cancellation
2. Agent calls `cancel_booking_hitl` tool
3. Tool prepares cancellation data and triggers interrupt
4. Telegram bot displays cancellation confirmation
5. User confirms/rejects cancellation
6. Graph resumes and processes cancellation or cancels

## Key Features

### LangGraph Integration
- Uses `interrupt()` function to pause execution
- Uses `Command(resume=data)` to continue after user input
- Requires checkpointer for persistent state management
- Proper state management for HITL data

### Telegram Bot UI
- Inline keyboard buttons for confirmations
- Formatted confirmation messages with booking details
- Real-time callback handling
- Error handling and user feedback

### Error Handling
- Graceful fallbacks for missing data
- Comprehensive error logging
- User-friendly error messages
- Timeout handling for confirmations

## Configuration

### Required Dependencies
- LangGraph with HITL support
- PostgreSQL checkpointer for state persistence
- Telegram Bot API for UI interactions

### Environment Variables
No additional environment variables required beyond existing setup.

## Usage Examples

### Hotel Booking with HITL
```python
# User message
"Pesan hotel di Jakarta untuk 25-27 Desember, 2 tamu, 1 kamar deluxe"

# System flow:
# 1. Agent calls book_hotel_room_hitl()
# 2. Tool prepares booking data
# 3. interrupt() pauses execution
# 4. Telegram shows confirmation UI
# 5. User clicks "✅ Ya, Lanjutkan"
# 6. Graph resumes with approved=True
# 7. execute_booking_node() creates actual booking
```

### Payment with HITL
```python
# User message  
"Bayar booking hotel ID 123 dengan kartu kredit"

# System flow:
# 1. Agent calls process_payment_hitl()
# 2. Tool prepares payment data
# 3. interrupt() pauses execution
# 4. Telegram shows payment confirmation
# 5. User clicks "✅ Ya, Bayar"
# 6. Graph resumes with approved=True
# 7. execute_payment_node() processes payment
```

## Benefits

1. **Better UX**: Clear confirmation UI instead of text-based prompts
2. **Reliability**: Proper state management with checkpointer
3. **Consistency**: Standardized confirmation flow across all actions
4. **Maintainability**: Separation of concerns between preparation and execution
5. **Extensibility**: Easy to add new HITL actions

## Testing

Run the test script to verify HITL functionality:
```bash
python test_hitl.py
```

## Migration Notes

### From Prompt-Based to HITL
- Removed manual confirmation prompts from `prompts.py`
- Replaced direct booking tools with HITL versions
- Updated agent instructions to use new HITL tools
- Added UI components for confirmations

### Backward Compatibility
- Original tools still available but not used by agents
- Database schema unchanged
- API endpoints maintain compatibility
