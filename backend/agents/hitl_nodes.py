"""
Human-in-the-Loop (HITL) nodes for booking, payment, and cancellation confirmations.
These nodes use LangGraph's interrupt() function to pause execution and wait for human approval.
"""

from typing import Dict, Any, Literal
from langgraph.types import interrupt, Command
from agents.state import State
import logging

logger = logging.getLogger(__name__)

def booking_confirmation_node(state: State) -> Dict[str, Any]:
    """
    HITL node for booking confirmation.
    Pauses execution and presents booking details for human approval.
    """
    pending_booking = state.get("pending_booking")
    if not pending_booking:
        logger.error("No pending booking data found in state")
        return {"hitl_action": None}
    
    # Prepare confirmation data for the user
    booking_type = pending_booking.get("type", "unknown")
    confirmation_data = {
        "action": "booking_confirmation",
        "booking_type": booking_type,
        "booking_details": pending_booking,
        "message": f"<PERSON><PERSON><PERSON><PERSON><PERSON> {booking_type.title()}",
        "question": "<PERSON><PERSON>kah Anda yakin ingin melanjutkan pemesanan dengan detail ini?"
    }
    
    # Interrupt execution and wait for human input
    human_decision = interrupt(confirmation_data)
    
    # Process human decision
    if human_decision and human_decision.get("approved"):
        logger.info(f"Booking confirmed by user for {booking_type}")
        return {
            "hitl_action": None,
            "pending_booking": None,
            "booking_approved": True,
            "booking_data": pending_booking
        }
    else:
        logger.info(f"Booking rejected by user for {booking_type}")
        return {
            "hitl_action": None,
            "pending_booking": None,
            "booking_approved": False,
            "booking_data": None
        }


def payment_confirmation_node(state: State) -> Dict[str, Any]:
    """
    HITL node for payment confirmation.
    Pauses execution and presents payment details for human approval.
    """
    pending_payment = state.get("pending_payment")
    if not pending_payment:
        logger.error("No pending payment data found in state")
        return {"hitl_action": None}
    
    # Prepare confirmation data for the user
    payment_type = pending_payment.get("type", "unknown")
    booking_id = pending_payment.get("booking_id", "unknown")
    amount = pending_payment.get("amount", 0)
    method = pending_payment.get("method", "unknown")
    
    confirmation_data = {
        "action": "payment_confirmation",
        "payment_type": payment_type,
        "booking_id": booking_id,
        "amount": amount,
        "payment_method": method,
        "payment_details": pending_payment,
        "message": f"Konfirmasi Pembayaran {payment_type.title()}",
        "question": f"Apakah Anda yakin ingin melanjutkan pembayaran sebesar {amount} dengan metode {method}?"
    }
    
    # Interrupt execution and wait for human input
    human_decision = interrupt(confirmation_data)
    
    # Process human decision
    if human_decision and human_decision.get("approved"):
        logger.info(f"Payment confirmed by user for booking {booking_id}")
        return {
            "hitl_action": None,
            "pending_payment": None,
            "payment_approved": True,
            "payment_data": pending_payment
        }
    else:
        logger.info(f"Payment rejected by user for booking {booking_id}")
        return {
            "hitl_action": None,
            "pending_payment": None,
            "payment_approved": False,
            "payment_data": None
        }


def cancellation_confirmation_node(state: State) -> Dict[str, Any]:
    """
    HITL node for cancellation confirmation.
    Pauses execution and presents cancellation details for human approval.
    """
    pending_cancellation = state.get("pending_cancellation")
    if not pending_cancellation:
        logger.error("No pending cancellation data found in state")
        return {"hitl_action": None}
    
    # Prepare confirmation data for the user
    cancellation_type = pending_cancellation.get("type", "unknown")
    booking_id = pending_cancellation.get("booking_id", "unknown")
    
    confirmation_data = {
        "action": "cancellation_confirmation",
        "cancellation_type": cancellation_type,
        "booking_id": booking_id,
        "cancellation_details": pending_cancellation,
        "message": f"Konfirmasi Pembatalan {cancellation_type.title()}",
        "question": f"Apakah Anda yakin ingin membatalkan pemesanan {booking_id}?"
    }
    
    # Interrupt execution and wait for human input
    human_decision = interrupt(confirmation_data)
    
    # Process human decision
    if human_decision and human_decision.get("approved"):
        logger.info(f"Cancellation confirmed by user for booking {booking_id}")
        return {
            "hitl_action": None,
            "pending_cancellation": None,
            "cancellation_approved": True,
            "cancellation_data": pending_cancellation
        }
    else:
        logger.info(f"Cancellation rejected by user for booking {booking_id}")
        return {
            "hitl_action": None,
            "pending_cancellation": None,
            "cancellation_approved": False,
            "cancellation_data": None
        }


def route_hitl_action(state: State) -> Literal["booking_confirmation", "payment_confirmation", "cancellation_confirmation", "continue"]:
    """
    Router function to determine which HITL confirmation node to execute.
    """
    hitl_action = state.get("hitl_action")

    if hitl_action == "booking":
        return "booking_confirmation"
    elif hitl_action == "payment":
        return "payment_confirmation"
    elif hitl_action == "cancellation":
        return "cancellation_confirmation"
    else:
        return "continue"


def execute_booking_node(state: State) -> Dict[str, Any]:
    """
    Execute confirmed booking after HITL approval.
    """
    booking_approved = state.get("booking_approved", False)
    booking_data = state.get("booking_data")

    if not booking_approved or not booking_data:
        return {
            "booking_approved": None,
            "booking_data": None,
            "booking_result": {"success": False, "message": "Pemesanan dibatalkan oleh pengguna"}
        }

    # Import here to avoid circular imports
    from tools.hitl_tools import execute_confirmed_booking
    import asyncio

    # Execute the booking
    try:
        result = asyncio.run(execute_confirmed_booking(booking_data))
        return {
            "booking_approved": None,
            "booking_data": None,
            "booking_result": result
        }
    except Exception as e:
        logger.error(f"Error executing booking: {e}")
        return {
            "booking_approved": None,
            "booking_data": None,
            "booking_result": {"success": False, "message": f"Gagal melakukan pemesanan: {str(e)}"}
        }


def execute_payment_node(state: State) -> Dict[str, Any]:
    """
    Execute confirmed payment after HITL approval.
    """
    payment_approved = state.get("payment_approved", False)
    payment_data = state.get("payment_data")

    if not payment_approved or not payment_data:
        return {
            "payment_approved": None,
            "payment_data": None,
            "payment_result": {"success": False, "message": "Pembayaran dibatalkan oleh pengguna"}
        }

    # Import here to avoid circular imports
    from tools.hitl_tools import execute_confirmed_payment
    import asyncio

    # Execute the payment
    try:
        result = asyncio.run(execute_confirmed_payment(payment_data))
        return {
            "payment_approved": None,
            "payment_data": None,
            "payment_result": result
        }
    except Exception as e:
        logger.error(f"Error executing payment: {e}")
        return {
            "payment_approved": None,
            "payment_data": None,
            "payment_result": {"success": False, "message": f"Gagal memproses pembayaran: {str(e)}"}
        }


def execute_cancellation_node(state: State) -> Dict[str, Any]:
    """
    Execute confirmed cancellation after HITL approval.
    """
    cancellation_approved = state.get("cancellation_approved", False)
    cancellation_data = state.get("cancellation_data")

    if not cancellation_approved or not cancellation_data:
        return {
            "cancellation_approved": None,
            "cancellation_data": None,
            "cancellation_result": {"success": False, "message": "Pembatalan dibatalkan oleh pengguna"}
        }

    # Import here to avoid circular imports
    from tools.hitl_tools import execute_confirmed_cancellation
    import asyncio

    # Execute the cancellation
    try:
        result = asyncio.run(execute_confirmed_cancellation(cancellation_data))
        return {
            "cancellation_approved": None,
            "cancellation_data": None,
            "cancellation_result": result
        }
    except Exception as e:
        logger.error(f"Error executing cancellation: {e}")
        return {
            "cancellation_approved": None,
            "cancellation_data": None,
            "cancellation_result": {"success": False, "message": f"Gagal membatalkan pemesanan: {str(e)}"}
        }


def route_execution(state: State) -> Literal["execute_booking", "execute_payment", "execute_cancellation", "continue"]:
    """
    Router function to determine which execution node to run after HITL confirmation.
    """
    if state.get("booking_approved") is not None:
        return "execute_booking"
    elif state.get("payment_approved") is not None:
        return "execute_payment"
    elif state.get("cancellation_approved") is not None:
        return "execute_cancellation"
    else:
        return "continue"
