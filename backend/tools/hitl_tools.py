"""
HITL-enabled booking and payment tools that use interrupt() for human confirmation.
These tools replace the prompt-based confirmation system with proper HITL workflow.
"""

from typing import Optional, Dict, Any
from langchain_core.tools import tool
from models.tools import DateModel, DateTimeModel, IdModel
from database.services import (
    get_hotel_by_id,
    get_available_rooms,
    create_hotel_booking,
    get_flight_by_id,
    create_flight_booking,
    get_tour_by_id,
    create_tour_booking,
    update_hotel_booking_payment,
    update_flight_booking_payment,
    update_tour_booking_payment,
    process_hotel_cancellation,
    process_flight_cancellation,
    process_tour_cancellation
)
from utils.handler import ValidationException
import logging

logger = logging.getLogger(__name__)

@tool
async def book_hotel_room_hitl(hotel_id: int, check_in_date: str, check_out_date: str, 
                              jumlah_tamu: int, jumlah_kamar: int, tipe_kamar: str,
                              nama_pemesan: str = "", email: str = "", telepon: str = "",
                              user_id: Optional[int] = None, catatan: Optional[str] = None) -> Dict[str, Any]:
    """
    Prepare hotel booking for HITL confirmation.
    This tool prepares booking data and triggers HITL confirmation instead of direct booking.
    
    Args:
        hotel_id: ID hotel yang akan dipesan
        check_in_date: Tanggal check-in (format: YYYY-MM-DD)
        check_out_date: Tanggal check-out (format: YYYY-MM-DD)
        jumlah_tamu: Jumlah tamu
        jumlah_kamar: Jumlah kamar yang dipesan
        tipe_kamar: Tipe kamar yang dipilih
        nama_pemesan: Nama pemesan
        email: Email pemesan
        telepon: Nomor telepon pemesan
        user_id: ID pengguna (opsional)
        catatan: Catatan tambahan (opsional)
    
    Returns:
        Dict containing booking preparation status and HITL trigger
    """
    try:
        # Validate required fields
        if not all([hotel_id, check_in_date, check_out_date, jumlah_tamu, jumlah_kamar, tipe_kamar]):
            raise ValidationException(
                message="Semua field wajib harus diisi untuk pemesanan hotel",
                detail={"missing_fields": "hotel_id, check_in_date, check_out_date, jumlah_tamu, jumlah_kamar, tipe_kamar"}
            )

        # Get hotel details
        hotel_data = await get_hotel_by_id(hotel_id)
        if not hotel_data:
            raise ValidationException(
                message=f"Hotel dengan ID {hotel_id} tidak ditemukan",
                detail={"hotel_id": hotel_id}
            )

        # Check room availability
        available_rooms = await get_available_rooms(hotel_id, check_in_date, check_out_date, jumlah_tamu)
        if not available_rooms:
            return {
                "success": False,
                "message": f"Tidak ada kamar tersedia di {hotel_data['nama']} untuk tanggal {check_in_date} - {check_out_date}",
                "hitl_action": None
            }

        # Find selected room type
        selected_room = None
        for room in available_rooms:
            if room['tipe_kamar'].lower() == tipe_kamar.lower():
                selected_room = room
                break

        if not selected_room:
            available_types = [room['tipe_kamar'] for room in available_rooms]
            return {
                "success": False,
                "message": f"Tipe kamar '{tipe_kamar}' tidak tersedia. Tipe kamar yang tersedia: {', '.join(available_types)}",
                "hitl_action": None
            }

        # Calculate total price
        from datetime import datetime
        check_in = datetime.strptime(check_in_date, "%Y-%m-%d")
        check_out = datetime.strptime(check_out_date, "%Y-%m-%d")
        nights = (check_out - check_in).days
        total_harga = selected_room['harga'] * nights * jumlah_kamar

        # Prepare booking data for HITL confirmation
        booking_data = {
            "type": "hotel",
            "hotel_id": hotel_id,
            "hotel_name": hotel_data['nama'],
            "hotel_location": hotel_data['lokasi'],
            "user_id": user_id,
            "nama_pemesan": nama_pemesan,
            "email": email,
            "telepon": telepon,
            "tanggal_mulai": check_in_date,
            "tanggal_akhir": check_out_date,
            "jumlah_tamu": jumlah_tamu,
            "jumlah_kamar": jumlah_kamar,
            "tipe_kamar": tipe_kamar,
            "harga_per_malam": selected_room['harga'],
            "jumlah_malam": nights,
            "total_harga": total_harga,
            "catatan": catatan,
            "room_details": selected_room
        }

        return {
            "success": True,
            "message": "Booking data prepared for confirmation",
            "hitl_action": "booking",
            "pending_booking": booking_data,
            "confirmation_required": True
        }

    except Exception as e:
        logger.error(f"Error preparing hotel booking: {e}")
        return {
            "success": False,
            "message": f"Terjadi kesalahan saat mempersiapkan pemesanan: {str(e)}",
            "hitl_action": None
        }


@tool
async def book_flight_hitl(flight_id: int, tanggal_keberangkatan: str, nama_pemesan: str = "", 
                          email: str = "", telepon: str = "", jumlah_penumpang: int = 0, 
                          kelas_penerbangan: str = None, user_id: Optional[int] = None, 
                          catatan: Optional[str] = None) -> Dict[str, Any]:
    """
    Prepare flight booking for HITL confirmation.
    This tool prepares booking data and triggers HITL confirmation instead of direct booking.
    """
    try:
        # Validate required fields
        if not all([flight_id, tanggal_keberangkatan, jumlah_penumpang]):
            raise ValidationException(
                message="Field wajib harus diisi: flight_id, tanggal_keberangkatan, jumlah_penumpang",
                detail={"missing_fields": "flight_id, tanggal_keberangkatan, jumlah_penumpang"}
            )

        # Get flight details
        flight_data = await get_flight_by_id(flight_id)
        if not flight_data:
            raise ValidationException(
                message=f"Penerbangan dengan ID {flight_id} tidak ditemukan",
                detail={"flight_id": flight_id}
            )

        # Calculate total price
        harga_per_tiket = flight_data.get('harga', 0)
        total_harga = harga_per_tiket * jumlah_penumpang

        # Prepare booking data for HITL confirmation
        booking_data = {
            "type": "flight",
            "flight_id": flight_id,
            "maskapai": flight_data.get('maskapai', ''),
            "rute": f"{flight_data.get('asal', '')} - {flight_data.get('tujuan', '')}",
            "user_id": user_id,
            "nama_pemesan": nama_pemesan,
            "email": email,
            "telepon": telepon,
            "tanggal_keberangkatan": tanggal_keberangkatan,
            "jumlah_penumpang": jumlah_penumpang,
            "kelas_penerbangan": kelas_penerbangan or "Ekonomi",
            "harga_per_tiket": harga_per_tiket,
            "total_harga": total_harga,
            "catatan": catatan,
            "flight_details": flight_data
        }

        return {
            "success": True,
            "message": "Flight booking data prepared for confirmation",
            "hitl_action": "booking",
            "pending_booking": booking_data,
            "confirmation_required": True
        }

    except Exception as e:
        logger.error(f"Error preparing flight booking: {e}")
        return {
            "success": False,
            "message": f"Terjadi kesalahan saat mempersiapkan pemesanan: {str(e)}",
            "hitl_action": None
        }


@tool
async def book_tour_hitl(tour_id: int, tanggal_tour: str, jumlah_peserta: int, 
                        nama_pemesan: str = "", email: str = "", telepon: str = "",
                        user_id: Optional[int] = None, catatan: Optional[str] = None) -> Dict[str, Any]:
    """
    Prepare tour booking for HITL confirmation.
    This tool prepares booking data and triggers HITL confirmation instead of direct booking.
    """
    try:
        # Validate required fields
        if not all([tour_id, tanggal_tour, jumlah_peserta]):
            raise ValidationException(
                message="Field wajib harus diisi: tour_id, tanggal_tour, jumlah_peserta",
                detail={"missing_fields": "tour_id, tanggal_tour, jumlah_peserta"}
            )

        # Get tour details
        tour_data = await get_tour_by_id(tour_id)
        if not tour_data:
            raise ValidationException(
                message=f"Paket tour dengan ID {tour_id} tidak ditemukan",
                detail={"tour_id": tour_id}
            )

        # Calculate total price
        harga_per_orang = tour_data.get('harga', 0)
        total_harga = harga_per_orang * jumlah_peserta

        # Prepare booking data for HITL confirmation
        booking_data = {
            "type": "tour",
            "tour_id": tour_id,
            "nama_tour": tour_data.get('nama', ''),
            "destinasi": tour_data.get('destinasi', ''),
            "user_id": user_id,
            "nama_pemesan": nama_pemesan,
            "email": email,
            "telepon": telepon,
            "tanggal_tour": tanggal_tour,
            "jumlah_peserta": jumlah_peserta,
            "harga_per_orang": harga_per_orang,
            "total_harga": total_harga,
            "catatan": catatan,
            "tour_details": tour_data
        }

        return {
            "success": True,
            "message": "Tour booking data prepared for confirmation",
            "hitl_action": "booking",
            "pending_booking": booking_data,
            "confirmation_required": True
        }

    except Exception as e:
        logger.error(f"Error preparing tour booking: {e}")
        return {
            "success": False,
            "message": f"Terjadi kesalahan saat mempersiapkan pemesanan: {str(e)}",
            "hitl_action": None
        }


@tool
async def process_payment_hitl(booking_id: int, booking_type: str, metode_pembayaran: str) -> Dict[str, Any]:
    """
    Prepare payment for HITL confirmation.
    This tool prepares payment data and triggers HITL confirmation instead of direct payment processing.

    Args:
        booking_id: ID pemesanan yang akan dibayar
        booking_type: Jenis pemesanan (hotel, flight, tour)
        metode_pembayaran: Metode pembayaran (transfer bank, kartu kredit, e-wallet)

    Returns:
        Dict containing payment preparation status and HITL trigger
    """
    try:
        # Validate payment method
        valid_methods = ['transfer bank', 'kartu kredit', 'e-wallet']
        if metode_pembayaran not in valid_methods:
            raise ValidationException(
                message=f"Metode pembayaran tidak valid. Pilihan: {', '.join(valid_methods)}",
                detail={"metode_pembayaran": metode_pembayaran}
            )

        # Get booking details based on type
        booking_data = None
        if booking_type == "hotel":
            from database.services import get_hotel_booking_by_id
            booking_data = await get_hotel_booking_by_id(booking_id)
        elif booking_type == "flight":
            from database.services import get_flight_booking_by_id
            booking_data = await get_flight_booking_by_id(booking_id)
        elif booking_type == "tour":
            from database.services import get_tour_booking_by_id
            booking_data = await get_tour_booking_by_id(booking_id)
        else:
            raise ValidationException(
                message=f"Tipe booking tidak valid: {booking_type}",
                detail={"booking_type": booking_type}
            )

        if not booking_data:
            raise ValidationException(
                message=f"Pemesanan {booking_type} dengan ID {booking_id} tidak ditemukan",
                detail={"booking_id": booking_id, "booking_type": booking_type}
            )

        # Check if already paid
        if booking_data.get('status_pembayaran') == 'paid':
            return {
                "success": False,
                "message": f"Pemesanan {booking_id} sudah dibayar",
                "hitl_action": None
            }

        # Prepare payment data for HITL confirmation
        payment_data = {
            "type": booking_type,
            "booking_id": booking_id,
            "method": metode_pembayaran,
            "amount": booking_data.get('total_harga', 0),
            "currency": "IDR",
            "booking_details": booking_data
        }

        return {
            "success": True,
            "message": "Payment data prepared for confirmation",
            "hitl_action": "payment",
            "pending_payment": payment_data,
            "confirmation_required": True
        }

    except Exception as e:
        logger.error(f"Error preparing payment: {e}")
        return {
            "success": False,
            "message": f"Terjadi kesalahan saat mempersiapkan pembayaran: {str(e)}",
            "hitl_action": None
        }


@tool
async def cancel_booking_hitl(booking_id: int, booking_type: str, alasan: Optional[str] = None) -> Dict[str, Any]:
    """
    Prepare booking cancellation for HITL confirmation.
    This tool prepares cancellation data and triggers HITL confirmation instead of direct cancellation.

    Args:
        booking_id: ID pemesanan yang akan dibatalkan
        booking_type: Jenis pemesanan (hotel, flight, tour)
        alasan: Alasan pembatalan (opsional)

    Returns:
        Dict containing cancellation preparation status and HITL trigger
    """
    try:
        # Get booking details based on type
        booking_data = None
        if booking_type == "hotel":
            from database.services import get_hotel_booking_by_id
            booking_data = await get_hotel_booking_by_id(booking_id)
        elif booking_type == "flight":
            from database.services import get_flight_booking_by_id
            booking_data = await get_flight_booking_by_id(booking_id)
        elif booking_type == "tour":
            from database.services import get_tour_booking_by_id
            booking_data = await get_tour_booking_by_id(booking_id)
        else:
            raise ValidationException(
                message=f"Tipe booking tidak valid: {booking_type}",
                detail={"booking_type": booking_type}
            )

        if not booking_data:
            raise ValidationException(
                message=f"Pemesanan {booking_type} dengan ID {booking_id} tidak ditemukan",
                detail={"booking_id": booking_id, "booking_type": booking_type}
            )

        # Check if already cancelled
        if booking_data.get('status') == 'cancelled':
            return {
                "success": False,
                "message": f"Pemesanan {booking_id} sudah dibatalkan",
                "hitl_action": None
            }

        # Prepare cancellation data for HITL confirmation
        cancellation_data = {
            "type": booking_type,
            "booking_id": booking_id,
            "alasan": alasan,
            "booking_details": booking_data,
            "refund_amount": booking_data.get('total_harga', 0) if booking_data.get('status_pembayaran') == 'paid' else 0
        }

        return {
            "success": True,
            "message": "Cancellation data prepared for confirmation",
            "hitl_action": "cancellation",
            "pending_cancellation": cancellation_data,
            "confirmation_required": True
        }

    except Exception as e:
        logger.error(f"Error preparing cancellation: {e}")
        return {
            "success": False,
            "message": f"Terjadi kesalahan saat mempersiapkan pembatalan: {str(e)}",
            "hitl_action": None
        }


@tool
async def execute_confirmed_booking(booking_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Execute the actual booking after HITL confirmation.
    This tool performs the actual database booking operation.
    """
    try:
        booking_type = booking_data.get("type")

        if booking_type == "hotel":
            # Prepare hotel booking data for database
            db_booking_data = {
                "hotel_id": booking_data["hotel_id"],
                "user_id": booking_data["user_id"],
                "nama_pemesan": booking_data["nama_pemesan"],
                "email": booking_data["email"],
                "telepon": booking_data["telepon"],
                "tanggal_mulai": booking_data["tanggal_mulai"],
                "tanggal_akhir": booking_data["tanggal_akhir"],
                "jumlah_tamu": booking_data["jumlah_tamu"],
                "jumlah_kamar": booking_data["jumlah_kamar"],
                "tipe_kamar": booking_data["tipe_kamar"],
                "total_harga": booking_data["total_harga"],
                "status": "pending",
                "metode_pembayaran": None,
                "status_pembayaran": "unpaid",
                "catatan": booking_data.get("catatan")
            }

            result = await create_hotel_booking(db_booking_data)

            return {
                "success": True,
                "message": f"✅ Pemesanan hotel berhasil! ID Booking: {result['id']}",
                "booking_id": result['id'],
                "booking_type": "hotel",
                "booking_details": result
            }

        elif booking_type == "flight":
            # Prepare flight booking data for database
            db_booking_data = {
                "flight_id": booking_data["flight_id"],
                "user_id": booking_data["user_id"],
                "nama_pemesan": booking_data["nama_pemesan"],
                "email": booking_data["email"],
                "telepon": booking_data["telepon"],
                "tanggal_keberangkatan": booking_data["tanggal_keberangkatan"],
                "jumlah_penumpang": booking_data["jumlah_penumpang"],
                "kelas_penerbangan": booking_data["kelas_penerbangan"],
                "total_harga": booking_data["total_harga"],
                "status": "pending",
                "metode_pembayaran": None,
                "status_pembayaran": "unpaid",
                "catatan": booking_data.get("catatan")
            }

            result = await create_flight_booking(db_booking_data)

            return {
                "success": True,
                "message": f"✅ Pemesanan penerbangan berhasil! ID Booking: {result['id']}",
                "booking_id": result['id'],
                "booking_type": "flight",
                "booking_details": result
            }

        elif booking_type == "tour":
            # Prepare tour booking data for database
            db_booking_data = {
                "tour_id": booking_data["tour_id"],
                "user_id": booking_data["user_id"],
                "nama_pemesan": booking_data["nama_pemesan"],
                "email": booking_data["email"],
                "telepon": booking_data["telepon"],
                "tanggal_tour": booking_data["tanggal_tour"],
                "jumlah_peserta": booking_data["jumlah_peserta"],
                "total_harga": booking_data["total_harga"],
                "status": "pending",
                "metode_pembayaran": None,
                "status_pembayaran": "unpaid",
                "catatan": booking_data.get("catatan")
            }

            result = await create_tour_booking(db_booking_data)

            return {
                "success": True,
                "message": f"✅ Pemesanan tour berhasil! ID Booking: {result['id']}",
                "booking_id": result['id'],
                "booking_type": "tour",
                "booking_details": result
            }
        else:
            raise ValidationException(
                message=f"Tipe booking tidak valid: {booking_type}",
                detail={"booking_type": booking_type}
            )

    except Exception as e:
        logger.error(f"Error executing confirmed booking: {e}")
        return {
            "success": False,
            "message": f"❌ Gagal melakukan pemesanan: {str(e)}",
            "booking_id": None
        }


@tool
async def execute_confirmed_payment(payment_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Execute the actual payment after HITL confirmation.
    This tool performs the actual database payment operation.
    """
    try:
        booking_id = payment_data["booking_id"]
        booking_type = payment_data["type"]
        metode_pembayaran = payment_data["method"]

        # Prepare payment update data
        payment_update_data = {
            "metode_pembayaran": metode_pembayaran,
            "status_pembayaran": "paid",
            "status": "confirmed"
        }

        if booking_type == "hotel":
            result = await update_hotel_booking_payment(booking_id, payment_update_data)
            return {
                "success": True,
                "message": f"✅ Pembayaran hotel berhasil! Booking ID: {booking_id}",
                "booking_id": booking_id,
                "payment_method": metode_pembayaran,
                "payment_details": result
            }

        elif booking_type == "flight":
            result = await update_flight_booking_payment(booking_id, payment_update_data)
            return {
                "success": True,
                "message": f"✅ Pembayaran penerbangan berhasil! Booking ID: {booking_id}",
                "booking_id": booking_id,
                "payment_method": metode_pembayaran,
                "payment_details": result
            }

        elif booking_type == "tour":
            result = await update_tour_booking_payment(booking_id, payment_update_data)
            return {
                "success": True,
                "message": f"✅ Pembayaran tour berhasil! Booking ID: {booking_id}",
                "booking_id": booking_id,
                "payment_method": metode_pembayaran,
                "payment_details": result
            }
        else:
            raise ValidationException(
                message=f"Tipe booking tidak valid: {booking_type}",
                detail={"booking_type": booking_type}
            )

    except Exception as e:
        logger.error(f"Error executing confirmed payment: {e}")
        return {
            "success": False,
            "message": f"❌ Gagal memproses pembayaran: {str(e)}",
            "booking_id": booking_id
        }


@tool
async def execute_confirmed_cancellation(cancellation_data: Dict[str, Any]) -> Dict[str, Any]:
    """
    Execute the actual cancellation after HITL confirmation.
    This tool performs the actual database cancellation operation.
    """
    try:
        booking_id = cancellation_data["booking_id"]
        booking_type = cancellation_data["type"]
        alasan = cancellation_data.get("alasan", "Dibatalkan oleh pengguna")

        if booking_type == "hotel":
            result = await process_hotel_cancellation(booking_id, alasan)
            return {
                "success": True,
                "message": f"✅ Pembatalan hotel berhasil! Booking ID: {booking_id}",
                "booking_id": booking_id,
                "cancellation_reason": alasan,
                "refund_info": result.get("refund_info", "Tidak ada refund")
            }

        elif booking_type == "flight":
            result = await process_flight_cancellation(booking_id, alasan)
            return {
                "success": True,
                "message": f"✅ Pembatalan penerbangan berhasil! Booking ID: {booking_id}",
                "booking_id": booking_id,
                "cancellation_reason": alasan,
                "refund_info": result.get("refund_info", "Tidak ada refund")
            }

        elif booking_type == "tour":
            result = await process_tour_cancellation(booking_id, alasan)
            return {
                "success": True,
                "message": f"✅ Pembatalan tour berhasil! Booking ID: {booking_id}",
                "booking_id": booking_id,
                "cancellation_reason": alasan,
                "refund_info": result.get("refund_info", "Tidak ada refund")
            }
        else:
            raise ValidationException(
                message=f"Tipe booking tidak valid: {booking_type}",
                detail={"booking_type": booking_type}
            )

    except Exception as e:
        logger.error(f"Error executing confirmed cancellation: {e}")
        return {
            "success": False,
            "message": f"❌ Gagal membatalkan pemesanan: {str(e)}",
            "booking_id": booking_id
        }
