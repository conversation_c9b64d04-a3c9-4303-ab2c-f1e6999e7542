#!/usr/bin/env python3
"""
Test script for Human-in-the-Loop (HITL) implementation in the travel agent.
This script tests the booking, payment, and cancellation HITL flows.
"""

import asyncio
import sys
import os

# Add backend to path
sys.path.append(os.path.join(os.path.dirname(__file__), 'backend'))

from agents.graph import build_graph
from langchain_core.messages import HumanMessage
from langgraph.types import Command

async def test_hitl_booking():
    """Test HITL booking flow."""
    print("🧪 Testing HITL Booking Flow...")
    
    try:
        # Build the graph
        graph = await build_graph()
        
        # Test hotel booking with HITL
        test_message = "Saya ingin pesan hotel di Jakarta untuk tanggal 2024-12-25 sampai 2024-12-27, 2 tamu, 1 kamar, tipe deluxe. <PERSON>a saya <PERSON>, email <EMAIL>, telepon 081234567890."
        
        state = {
            'messages': [HumanMessage(content=test_message)],
            'user_context': {
                'user_id': 123,
                'nama': '<PERSON>',
                'email': '<EMAIL>',
                'telepon': '081234567890'
            }
        }
        
        config = {"configurable": {"thread_id": "test-hitl-booking", "recursion_limit": 25}}
        
        print("📤 Sending booking request...")
        response = await graph.ainvoke(input=state, config=config)
        
        # Check for interrupt
        interrupt_data = response.get('__interrupt__')
        if interrupt_data:
            print("✅ HITL interrupt detected!")
            print(f"📋 Interrupt data: {interrupt_data}")
            
            # Simulate user approval
            print("👤 Simulating user approval...")
            resume_response = await graph.ainvoke(Command(resume={"approved": True}), config=config)
            print(f"✅ Resume response: {resume_response.get('messages', [])[-1].content if resume_response.get('messages') else 'No message'}")
        else:
            print("❌ No HITL interrupt detected")
            print(f"📝 Response: {response}")
            
    except Exception as e:
        print(f"❌ Error testing HITL booking: {e}")
        import traceback
        traceback.print_exc()

async def test_hitl_payment():
    """Test HITL payment flow."""
    print("\n🧪 Testing HITL Payment Flow...")
    
    try:
        # Build the graph
        graph = await build_graph()
        
        # Test payment with HITL
        test_message = "Saya ingin bayar booking hotel dengan ID 1 menggunakan transfer bank"
        
        state = {
            'messages': [HumanMessage(content=test_message)],
            'user_context': {
                'user_id': 123,
                'nama': 'John Doe',
                'email': '<EMAIL>'
            }
        }
        
        config = {"configurable": {"thread_id": "test-hitl-payment", "recursion_limit": 25}}
        
        print("📤 Sending payment request...")
        response = await graph.ainvoke(input=state, config=config)
        
        # Check for interrupt
        interrupt_data = response.get('__interrupt__')
        if interrupt_data:
            print("✅ HITL interrupt detected!")
            print(f"📋 Interrupt data: {interrupt_data}")
            
            # Simulate user approval
            print("👤 Simulating user approval...")
            resume_response = await graph.ainvoke(Command(resume={"approved": True}), config=config)
            print(f"✅ Resume response: {resume_response.get('messages', [])[-1].content if resume_response.get('messages') else 'No message'}")
        else:
            print("❌ No HITL interrupt detected")
            print(f"📝 Response: {response}")
            
    except Exception as e:
        print(f"❌ Error testing HITL payment: {e}")
        import traceback
        traceback.print_exc()

async def main():
    """Main test function."""
    print("🚀 Starting HITL Tests...")
    
    await test_hitl_booking()
    await test_hitl_payment()
    
    print("\n✅ HITL Tests completed!")

if __name__ == "__main__":
    asyncio.run(main())
